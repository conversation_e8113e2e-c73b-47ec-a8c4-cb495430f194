# Supabase
# https://app.supabase.com/project/_/settings/api
EXPO_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=placeholder-anon-key
SUPABASE_SERVICE_ROLE_KEY=placeholder-service-role-key

# API
EXPO_PUBLIC_API_URL=http://localhost:8787

# Site URL
# Used to generate invitation links and billing return URLs
# It should be the hostname of your application with protocol (e.g. https://example.com)
EXPO_PUBLIC_SITE_URL=http://localhost:8081

# Stripe
# https://dashboard.stripe.com/test/apikeys
STRIPE_API_KEY=placeholder-stripe-secret-key
STRIPE_WEBHOOK_SIGNING_SECRET=placeholder-stripe-webhook-signing-secret
DEFAULT_TRIAL_PLAN_LOOKUP_KEY=placeholder-default-trial-plan-lookup-key
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=placeholder-stripe-publishable-key