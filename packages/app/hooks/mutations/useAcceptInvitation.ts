import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAxios } from "@/hooks/useAxios";
import { TEAM_MEMBERS_QUERY_KEY } from "../useTeamAccountMembers";
import { createTeamInvitesApi } from "@/utils/teamInvitesApi";

interface AcceptInvitationProps {
  token: string;
}

interface AcceptInvitationResponse {
  success: boolean;
  team_id: string;
  team_role: string;
  team_name: string;
  slug: string;
  message: string;
}

export function useAcceptInvitation() {
  const queryClient = useQueryClient();
  const { axios } = useAxios();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useMutation<AcceptInvitationResponse, Error, AcceptInvitationProps>({
    mutationFn: async ({ token }) => {
      try {
        console.log("Accepting invitation with token:", token);
        const response = await teamInvitesApi.acceptTeamInvite({ token });

        if (!response.success) {
          throw new Error("Failed to accept invitation");
        }

        return response;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        console.error("Error in acceptInvitation:", errorMessage);
        throw new Error(errorMessage || "Failed to accept invitation");
      }
    },
    onSuccess: () => {
      // Invalidate the team members query to refresh the list
      queryClient.invalidateQueries({ queryKey: [TEAM_MEMBERS_QUERY_KEY] });
    },
  });
}
