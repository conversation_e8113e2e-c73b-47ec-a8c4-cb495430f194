import * as React from "react";
import { ActivityIndicator, Platform, TextInput, Alert } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { Box, Center, Heading } from "@/components/ui";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { useAcceptInvitation } from "@/hooks/mutations/useAcceptInvitation";
import { useLookupInvitation } from "@/hooks/useLookupInvitation";
import Reanimated, { FadeIn } from "react-native-reanimated";
import UsersIcon from "@/assets/icons/user-multiple-02.svg";
import { Link } from "expo-router";

export default function AcceptInviteScreen() {
  const { token, email } = useLocalSearchParams<{ token: string; email: string }>();
  const router = useRouter();
  const { session, loading: authLoading, sendMagicLink, verifyOtp } = useSupabaseAuth();
  const acceptInvitationMutation = useAcceptInvitation();

  // Use the lookup hook to get invitation details
  const { data: invitationData, isLoading: isLoadingInvitation, error: invitationError } = useLookupInvitation(token);

  // State for the new workflow
  const [isSigningIn, setIsSigningIn] = React.useState(false);
  const [otpSent, setOtpSent] = React.useState(false);
  const [otpCode, setOtpCode] = React.useState("");
  const [inviteEmail, setInviteEmail] = React.useState(email || "");

  // Set the invite email from the URL parameter or invitation data
  React.useEffect(() => {
    if (email && !inviteEmail) {
      setInviteEmail(email);
    } else if (invitationData?.email && !inviteEmail) {
      setInviteEmail(invitationData.email);
    }
  }, [email, invitationData?.email, inviteEmail]);

  // Handle authentication after successful login
  React.useEffect(() => {
    if (session && otpSent) {
      // User has successfully logged in, now accept the invitation
      handleAcceptInvitation();
    }
  }, [session, otpSent]);

  // Handle accepting invitation
  const handleAcceptInvitation = async () => {
    if (!token) {
      Alert.alert("Erro", "Token do convite não encontrado.");
      return;
    }

    console.log("Attempting to accept invitation with token:", token);
    console.log("Authenticated as:", session?.user?.email);

    try {
      const result = await acceptInvitationMutation.mutateAsync({ token });
      console.log("Invitation accepted successfully:", result);

      // Show success message
      Alert.alert("Sucesso", "Convite aceito com sucesso!");

      // Redirect to dashboard after successful acceptance
      setTimeout(() => {
        router.replace("/");
      }, 1000);
    } catch (error) {
      console.error("Error accepting invitation:", error);
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";

      if (errorMessage.includes("email")) {
        Alert.alert(
          "Erro de Email",
          "Este convite foi enviado para um email diferente. Por favor, faça login com o email correto para aceitar o convite."
        );
      } else {
        Alert.alert(
          "Erro",
          "Erro ao aceitar convite. Este convite pode estar expirado (limite de 24 horas) ou ser inválido."
        );
      }
    }
  };

  // Handle sending magic link
  const handleSendMagicLink = async () => {
    if (!inviteEmail) {
      Alert.alert("Erro", "Email é obrigatório.");
      return;
    }

    setIsSigningIn(true);
    try {
      await sendMagicLink(inviteEmail);
      setOtpSent(true);
      Alert.alert("Email Enviado", "Verifique seu email e digite o código de verificação.");
    } catch (error) {
      console.error("Error sending magic link:", error);
      Alert.alert("Erro", "Erro ao enviar email de verificação.");
    } finally {
      setIsSigningIn(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async () => {
    if (!otpCode || !inviteEmail) {
      Alert.alert("Erro", "Código de verificação e email são obrigatórios.");
      return;
    }

    setIsSigningIn(true);
    try {
      await verifyOtp(inviteEmail, otpCode);
      // The useEffect will handle accepting the invitation after successful login
    } catch (error) {
      console.error("Error verifying OTP:", error);
      Alert.alert("Erro", "Código de verificação inválido.");
    } finally {
      setIsSigningIn(false);
    }
  };

  // Handle the main action button
  const handleMainAction = () => {
    if (session) {
      // User is already logged in, accept invitation directly
      handleAcceptInvitation();
    } else if (otpSent) {
      // OTP has been sent, verify it
      handleVerifyOtp();
    } else {
      // Send magic link to start login process
      handleSendMagicLink();
    }
  };

  // Loading state
  if (authLoading || isLoadingInvitation) {
    return (
      <Center className="flex-1 bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4">Carregando...</Text>
      </Center>
    );
  }

  // Error state
  if (invitationError || !token) {
    return (
      <Center className="flex-1 bg-background px-6">
        <Reanimated.View entering={FadeIn}>
          <Box className="items-center">
            <UsersIcon />
            <Heading size="xl" className="mt-4 text-center">
              Convite Inválido
            </Heading>
            <Text className="mt-2 text-center text-text-light">
              Este convite não é válido ou pode ter expirado.
            </Text>
            <Link href="/entrar" asChild>
              <Button className="mt-6" variant="outline">
                <Text>Ir para Login</Text>
              </Button>
            </Link>
          </Box>
        </Reanimated.View>
      </Center>
    );
  }

  // Invitation not found or inactive
  if (!invitationData?.active) {
    return (
      <Center className="flex-1 bg-background px-6">
        <Reanimated.View entering={FadeIn}>
          <Box className="items-center">
            <UsersIcon />
            <Heading size="xl" className="mt-4 text-center">
              Convite Expirado
            </Heading>
            <Text className="mt-2 text-center text-text-light">
              Este convite expirou ou não é mais válido.
            </Text>
            <Link href="/entrar" asChild>
              <Button className="mt-6" variant="outline">
                <Text>Ir para Login</Text>
              </Button>
            </Link>
          </Box>
        </Reanimated.View>
      </Center>
    );
  }

  return (
    <Center className="flex-1 bg-background px-6">
      <Reanimated.View entering={FadeIn}>
        <Box className="items-center">
          <UsersIcon />
          <Heading size="xl" className="mt-4 text-center">
            Convite para {invitationData.account_name}
          </Heading>
          <Text className="mt-2 text-center text-text-light">
            {session
              ? `Você foi convidado para se juntar à equipe ${invitationData.account_name}.`
              : `Você foi convidado para se juntar à equipe ${invitationData.account_name}. Faça login para aceitar o convite.`
            }
          </Text>

          {!session && (
            <Box className="mt-6 w-full">
              <Text className="mb-2 font-medium">
                Email do convite:
              </Text>
              <TextInput
                value={inviteEmail}
                onChangeText={setInviteEmail}
                placeholder="Digite seu email"
                keyboardType="email-address"
                autoCapitalize="none"
                style={{
                  borderWidth: 1,
                  borderColor: '#ccc',
                  borderRadius: 8,
                  padding: 12,
                  marginBottom: 16,
                  backgroundColor: 'white'
                }}
                editable={!otpSent}
              />

              {otpSent && (
                <>
                  <Text className="mb-2 font-medium">
                    Código de verificação:
                  </Text>
                  <TextInput
                    value={otpCode}
                    onChangeText={setOtpCode}
                    placeholder="Digite o código de 6 dígitos"
                    keyboardType="number-pad"
                    maxLength={6}
                    style={{
                      borderWidth: 1,
                      borderColor: '#ccc',
                      borderRadius: 8,
                      padding: 12,
                      marginBottom: 16,
                      backgroundColor: 'white'
                    }}
                  />
                </>
              )}
            </Box>
          )}

          <Button
            className="mt-6 w-full"
            onPress={handleMainAction}
            disabled={isSigningIn || acceptInvitationMutation.isPending}
          >
            {isSigningIn || acceptInvitationMutation.isPending ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text>
                {session
                  ? "Aceitar Convite"
                  : otpSent
                    ? "Verificar Código"
                    : "Enviar Código de Verificação"
                }
              </Text>
            )}
          </Button>

          {otpSent && !session && (
            <Button
              className="mt-3 w-full"
              variant="outline"
              onPress={() => {
                setOtpSent(false);
                setOtpCode("");
              }}
            >
              <Text>Voltar</Text>
            </Button>
          )}

          <Link href="/entrar" asChild>
            <Button className="mt-3 w-full" variant="ghost">
              <Text>Já tem uma conta? Fazer login</Text>
            </Button>
          </Link>
        </Box>
      </Reanimated.View>
    </Center>
  );
}
