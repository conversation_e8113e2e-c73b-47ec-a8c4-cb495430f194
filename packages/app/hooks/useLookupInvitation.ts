import { useQuery } from "@tanstack/react-query";
import { useAxios } from "@/hooks/useAxios";
import { createTeamInvitesApi } from "@/utils/teamInvitesApi";

// Define the LookupInvitationResponse type
export interface LookupInvitationResponse {
  active: boolean;
  account_name: string;
  team_id?: string;
  account_role?: string;
  email?: string;
}

export const INVITATION_LOOKUP_KEY = "invitation_lookup";

export function useLookupInvitation(token: string | undefined | null) {
  const { axios } = useAxios();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useQuery<LookupInvitationResponse, Error>({
    queryKey: [INVITATION_LOOKUP_KEY, token],
    queryFn: async () => {
      if (!token) {
        throw new Error("No invitation token provided");
      }

      try {
        const response = await teamInvitesApi.lookupTeamInvite(token);

        if (!response.active) {
          return { active: false, account_name: '' };
        }

        // Return the invitation data
        return {
          active: true,
          account_name: response.invitation.team_name || 'Time',
          team_id: response.invitation.team_slug, // Using slug as team identifier
          account_role: response.invitation.team_role,
          email: response.invitation.email
        };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        console.error("Error in lookupInvitation:", errorMessage);

        // If it's a 404 error, return inactive invitation instead of throwing
        if (errorMessage.includes('404') || errorMessage.includes('not found')) {
          return { active: false, account_name: '' };
        }

        throw new Error(errorMessage || "Failed to lookup invitation");
      }
    },
    enabled: !!token,
    retry: false, // Don't retry on error since invalid tokens should fail immediately
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
